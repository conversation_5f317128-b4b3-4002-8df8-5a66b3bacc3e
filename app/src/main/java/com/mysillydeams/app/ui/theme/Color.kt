package com.mysillydeams.app.ui.theme

import androidx.compose.ui.graphics.Color

// Light Theme Colors
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

// Dark Theme Colors
val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// MySillyDreams Brand Colors
val BrandPurple = Color(0xFF8B5CF6)
val BrandPink = Color(0xFFEC4899)
val BrandIndigo = Color(0xFF6366F1)

// Neumorphic Colors - Light
val NeumorphicLight = Color(0xFFF7F7F7)
val NeumorphicLightShadow = Color(0xFFD1D9E6)
val NeumorphicLightHighlight = Color(0xFFFFFFFF)

// Neumorphic Colors - Dark
val NeumorphicDark = Color(0xFF2D3748)
val NeumorphicDarkShadow = Color(0xFF1A202C)
val NeumorphicDarkHighlight = Color(0xFF4A5568)

// Gradient Colors
val GradientStart = Color(0xFF667EEA)
val GradientEnd = Color(0xFF764BA2)

// Google Colors
val GoogleBlue = Color(0xFF4285F4)
val GoogleRed = Color(0xFFEA4335)
val GoogleYellow = Color(0xFFFBBC05)
val GoogleGreen = Color(0xFF34A853)

// Status Colors
val SuccessGreen = Color(0xFF10B981)
val ErrorRed = Color(0xFFEF4444)
val WarningYellow = Color(0xFFF59E0B)

// Gray Scale
val Gray50 = Color(0xFFFAFAFA)
val Gray100 = Color(0xFFF5F5F5)
val Gray200 = Color(0xFFEEEEEE)
val Gray300 = Color(0xFFE0E0E0)
val Gray400 = Color(0xFFBDBDBD)
val Gray500 = Color(0xFF9E9E9E)
val Gray600 = Color(0xFF757575)
val Gray700 = Color(0xFF616161)
val Gray800 = Color(0xFF424242)
val Gray900 = Color(0xFF212121)
